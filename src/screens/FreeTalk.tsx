import React, { useEffect } from "react";
import { StatusBar } from "expo-status-bar";
import ChatComponent from "../components/chat/ChatComponent";
import { useIsFocused } from "@react-navigation/native";
import { Keyboard } from "react-native";

export default function FreeTalk({ route, navigation }) {
  const item = {
    _id: "67ba493b70ebab475c063a79",
    title: "free talk",
    scenario:
      "You are an AI conversation partner designed to engage users in a natural, free-flowing conversation. Your goal is to start a friendly and open-ended chat that makes the user feel comfortable and eager to respond. Avoid robotic or overly formal phrasing. Use dynamic opening",
  };
  const isFocused = useIsFocused();
  useEffect(() => {
    navigation.getParent().setOptions({ tabBarStyle: { display: "none" } });
    return () =>
      navigation.getParent().setOptions({ tabBarStyle: { display: "flex" } });
  }, [navigation]);
  return (
    <>
      {isFocused && <StatusBar style="light" />}

      <ChatComponent
        route={{
          params: {
            topicItem: item,
          },
        }}
        navigation={navigation}
      />
    </>
  );
}

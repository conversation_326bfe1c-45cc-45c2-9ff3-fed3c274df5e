import React, { useState, useEffect } from "react";
import { View, ScrollView } from "react-native";
import { StatusBar } from "expo-status-bar";
import {
  useIsFocused,
  useRoute,
  useNavigation,
} from "@react-navigation/native";
import styles from "../styles/homeStyle";
import FreeTalkCard from "../components/homeCards/FreeTalkCard";
import VoiceAssistantCard from "../components/homeCards/VoiceAssistantCard";
import RolePlayCard from "../components/homeCards/RolePlayCard";
import DailyLessonCard from "../components/homeCards/DailyLessonCard";
import GamificationComponent from "../components/homeCards/GamAndStreak";
import LastLessonCard from "../components/homeCards/LastLessonCard";
import requestHelper from "../utils/requestHelper";
import { LastCompletedLessonData } from "../types/LastLesson";

const PlanScreen = () => {
  const isFocused = useIsFocused();
  const route = useRoute();
  const navigation = useNavigation<any>();
  const [gamiKey, setGamiKey] = useState(0);
  const [lastCompletedLesson, setLastCompletedLesson] =
    useState<LastCompletedLessonData | null>(null);

  const fetchLastCompletedLesson = async () => {
    try {
      const response = await requestHelper("get", "/ai/last-completed-lesson");
      if (response && response.assessment) {
        setLastCompletedLesson(response as LastCompletedLessonData);
      }
    } catch (error) {
      console.error("Error fetching last completed lesson:", error);
    }
  };

  useEffect(() => {
    // Fetch last completed lesson data
    fetchLastCompletedLesson();
  }, []);

  useEffect(() => {
    // If navigated with refreshGamification param, reload GamificationComponent
    const params = (route as any).params;
    if (params && params.refreshGamification) {
      fetchLastCompletedLesson();
      setGamiKey((k) => k + 1);
      // Remove the param so it doesn't trigger again
      navigation.setParams({ refreshGamification: undefined });
    }
  }, [route]);

  return (
    <View style={styles.container}>
      {isFocused && <StatusBar style="dark" />}
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <GamificationComponent key={gamiKey} />
        {lastCompletedLesson && (
          <LastLessonCard
            key={gamiKey + 1}
            lastCompletedLesson={lastCompletedLesson}
          />
        )}
        <DailyLessonCard
          key={gamiKey + 2}
          lastCompletedLesson={lastCompletedLesson}
        />

        {/* <VoiceAssistantCard /> */}
        <FreeTalkCard />
        <RolePlayCard />
      </ScrollView>
    </View>
  );
};

export default PlanScreen;

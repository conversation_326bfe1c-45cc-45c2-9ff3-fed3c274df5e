import "react-native-get-random-values";
import React, { useState, useEffect } from "react";
import * as Speech from "expo-speech";
import { View } from "react-native";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { initNewChat } from "../../store/slices/modelSlice";
import { loadModel } from "../../utils/whisperModel";
import ChatUi from "./chatUi";
import { useChatState } from "./chatHooks";
// CongratulationsAnimation removed as we're directly showing the assessment screen
import {
  startTopic,
  chatRequest,
  requestAgain,
  submitChat,
  addReqToHistory,
} from "./chatActions";
import { handleRecordingComplete } from "../handleTranscription";

interface ChatProps {
  route: any;
  navigation: any;
  onTranscriptionUpdate?: (result: any[]) => void;
}

export default function Chat({ route, navigation, onTranscriptionUpdate }: ChatProps) {
  const { topicItem, comesFrom } = route.params;
  const dispatch = useAppDispatch();
  const { level, isPremium, coins } = useAppSelector((state) => state.auth);

  // Use the custom hook to manage chat state
  const {
    chatId,
    setChatId,
    inputValue,
    setInputValue,
    isSpeaking,
    setIsSpeaking,
    isModalVisible,
    setIsModalVisible,
    playSpeech,
    setPlaySpeech,
    requested,
    setRequested,
    scores,
    setScores,
    textSubmitted,
    setTextSubmitted,
    chatFinished,
    setChatFinished,
    hasReachedDailyLimit,
    setHasReachedDailyLimit,
    transcriptionResult,
    setTranscriptionResult,
    Session,
    newChat,
    approvedNewChat,
    setApprovedNewChat,
    keySize,
    // checkIfReachedLastMessage and showCongratulations removed
  } = useChatState(topicItem, navigation, comesFrom);

  // Load model if needed
  // useEffect(() => {
  //   if (!Session) {
  //     loadModel();
  //   }
  // }, []);

  // Start topic when component loads or when topicItem changes
  useEffect(() => {
    startTopic(
      newChat,
      topicItem,
      setChatId,
      setTranscriptionResult,
      setRequested,
      setScores,
      dispatch,
      initNewChat,
      keySize,
      level,
      approvedNewChat,
      isPremium,
      coins
    );
  }, [topicItem._id]);

  // Start topic when newChat changes
  useEffect(() => {
    if (approvedNewChat) {
      startTopic(
        newChat,
        topicItem,
        setChatId,
        setTranscriptionResult,
        setRequested,
        setScores,
        dispatch,
        initNewChat,
        keySize,
        level,
        approvedNewChat,
        isPremium,
        coins
      );
    }
  }, [approvedNewChat]);

  // Handle transcription results
  useEffect(() => {
    if (transcriptionResult.length) {
      // Call the callback if provided
      if (onTranscriptionUpdate) {
        onTranscriptionUpdate(transcriptionResult);
      }

      const last = transcriptionResult[transcriptionResult.length - 1];
      if (last?.type === "loading" && !requested) {
        chatRequest(
          last.key,
          transcriptionResult,
          chatId,
          topicItem,
          setTranscriptionResult,
          setPlaySpeech,
          setHasReachedDailyLimit,
          () => {}, // Empty function instead of setShowCongratulations
          level,
          navigation,
          setChatFinished
        );
      }
      if (last?.type === "res") {
        const trimmedContainsQuestionMark = /\?|tell/i.test(last.text);
        const words = last.text.split(/\s+/);
        if (!trimmedContainsQuestionMark && !requested && words.length < 10) {
          requestAgain(
            transcriptionResult,
            chatId,
            topicItem,
            setRequested,
            setTranscriptionResult,
            setPlaySpeech,
            setHasReachedDailyLimit
          );
        }
      }
    }
  }, [transcriptionResult]);

  // Handle finished chat with scores
  // useEffect(() => {
  //   if (
  //     (scores?.average || chatFinished) &&
  //     !newChat &&
  //     topicItem.title != "free talk"
  //   ) {
  //     const lastMessage = transcriptionResult[transcriptionResult.length - 1];
  //     if (lastMessage?.type === "res") {
  //       setTranscriptionResult((prevState) =>
  //         prevState.slice(0, prevState.length - 1)
  //       );
  //     }
  //   }
  // }, [transcriptionResult]);

  // Handle chat submission
  const submit = () => {
    submitChat(
      inputValue,
      setTextSubmitted,
      setInputValue,
      setIsSpeaking,
      (text, isTopic) =>
        addReqToHistory(
          text,
          isTopic,
          transcriptionResult,
          setTranscriptionResult,
          setRequested
        )
    );
  };

  const recordingComplete = (uri) => {
    handleRecordingComplete(
      transcriptionResult,
      setTranscriptionResult,
      chatId,
      uri,
      setTextSubmitted
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <ChatUi
        transcriptionResult={transcriptionResult}
        Speech={Speech}
        isSpeaking={isSpeaking}
        submit={submit}
        inputValue={inputValue}
        setInputValue={setInputValue}
        isModalVisible={isModalVisible}
        setIsModalVisible={setIsModalVisible}
        topicItem={topicItem}
        setIsSpeaking={setIsSpeaking}
        chatId={chatId}
        playSpeech={playSpeech}
        scores={scores}
        textSubmitted={textSubmitted}
        chatFinished={chatFinished}
        setChatFinished={setChatFinished}
        newChat={newChat}
        setScores={setScores}
        hasReachedDailyLimit={hasReachedDailyLimit}
        navigation={navigation}
        recordingComplete={recordingComplete}
        approvedNewChat={approvedNewChat}
        setApprovedNewChat={setApprovedNewChat}
      />

      {/* Congratulations Animation removed - directly showing assessment screen */}
    </View>
  );
}

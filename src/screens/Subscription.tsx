import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Pressable,
  Linking,
  Platform,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { useIsFocused, useNavigation } from "@react-navigation/native";
import requestHelper from "../utils/requestHelper";
import { convertToPersianNumber } from "../utils/helpers";
import Toast from "react-native-toast-message";
import { useAppDispatch } from "../store/hooks";
import { updatePremium } from "../store/slices/authSlice";
import constants from "../utils/constants";
import { useBazaar } from "@cafebazaar/react-native-poolakey";
import { MaterialIcons } from "@expo/vector-icons";

const SubscriptionScreen = () => {
  const isFocused = useIsFocused();
  const dispatch = useAppDispatch();
  const navigation = useNavigation();

  interface Plan {
    id: string;
    fa: string;
    price: number;
    default: boolean;
    discount: number;
  }

  interface PurchaseResult {
    orderId: string;
    packageName: string;
    productId: string;
    purchaseToken: string;
  }

  const [plans, setPlans] = useState<Plan[]>([]);
  const [activePlan, setActivePlan] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  let bazaar: any = null;
  if (constants.marketStore === "CAFEBAZAAR") {
    bazaar = useBazaar(
      "MIHNMA0GCSqGSIb3DQEBAQUAA4G7ADCBtwKBrwCzgJTH9oJc6wreOOgANPIweF6jFkpVHnPyusQtbU0GbsTWXvjcrt/rfyCE79jmFg1phPvY0ujJZlTTi5AMy4sc8NS8MWMbmP4+LYwolYkvrRchwvRq8JqQ+vl1Osfa9IQZ4ZhIT3ACoQv+8kpXfINwMLA9R6c/MNcVRpkZYdTpfa3+e/SJQZrKvBa54QqncGFMlepUlkLziAjD4p34hZF7Uw71lqeoOgA+kpNtwxcCAwEAAQ=="
    );
  }

  useEffect(() => {
    const getPrices = async () => {
      try {
        // Get plans from backend
        const response = await requestHelper(
          "get",
          "/users/subscription/plans",
          null,
          null,
          null,
          true
        );
        setPlans(response);
        const defaultPlan = response.find((plan: Plan) => plan.default);
        setActivePlan(defaultPlan.id);
      } catch (error) {
        console.error("Error fetching plans:", error);
        Toast.show({
          type: "error",
          text1: "خطا در دریافت اطلاعات اشتراک",
          bottomOffset: 100,
          position: "bottom",
        });
      }
    };
    getPrices();
  }, []);

  const changeActivePlan = (id: string) => {
    setActivePlan(id);
    // const updatedPlans = plans.map((p: Plan) => {
    //   if (p.id === id) {
    //     return { ...p, default: true };
    //   }
    //   return { ...p, default: false };
    // });
    // setPlans(updatedPlans);
  };

  const handlePurchase = async () => {
    setIsLoading(true);
    try {
      if (Platform.OS === "android" && constants.marketStore === "CAFEBAZAAR") {
      const planId = __DEV__ ? "test2" : activePlan;
        const purchaseResult = await purchaseSubscriptionBazaar(planId);
        const response = await requestHelper(
          "post",
          "/users/pay/cafebazaar/verify",
          {
            packageName: purchaseResult.packageName,
            productId: purchaseResult.productId,
            purchaseToken: purchaseResult.purchaseToken,
          }
        );

        if (response.status) {
          navigation.navigate("profile");
        }
      } else {
        // Handle Zarinpal/Google Play payment
        const response = await requestHelper(
          "post",
          "/users/pay/zarinpal/open",
          {
            planId: activePlan,
          }
        );
        Linking.openURL(response);
      }
      Toast.show({
        type: "success",
        text1: "اشتراک شما با موفقیت فعال شد",
        bottomOffset: 100,
        position: "bottom",
      });
    } catch (error) {
      console.error("Purchase error:", error);
      Toast.show({
        type: "error",
        text1: "خطا در پرداخت",
        bottomOffset: 100,
        position: "bottom",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const purchaseSubscriptionBazaar = async (
    sku: string
  ): Promise<PurchaseResult> => {
    if (Platform.OS !== "android") {
      throw new Error("Cafebazaar billing is only available on Android");
    }
    if (!bazaar) {
      throw new Error("Bazaar is not initialized");
    }
    try {
      const purchase = await bazaar.subscribeProduct(sku);
      console.log("purchase", purchase);
      return {
        orderId: purchase.orderId,
        packageName: purchase.packageName,
        productId: purchase.productId,
        purchaseToken: purchase.purchaseToken,
      };
    } catch (error) {
      console.error("Failed to purchase subscription:", error);
      throw error;
    }
  };

  return (
    <View style={styles.containerOuter}>
      {/* Close Icon */}
      <TouchableOpacity
        style={{ position: "absolute", top: 55, left: 20, zIndex: 10 }}
        onPress={() => navigation.goBack()}
      >
        <MaterialIcons name="close" size={27} color="#fff" />
      </TouchableOpacity>
      <ScrollView contentContainerStyle={styles.container}>
        {isFocused && <StatusBar style="light" />}

        <Text style={styles.title}>اشتراک پرمیوم</Text>
        <Text style={styles.subtitle}>دسترسی نامحدود به تمام امکانات</Text>

        <View style={styles.planContainer}>
          {plans.map((plan: Plan) => (
            <Pressable
              key={plan.id}
              style={[
                styles.planBox,
                activePlan === plan.id && styles.activePlan,
              ]}
              onPress={() => changeActivePlan(plan.id)}
            >
              <View>
                <Text style={styles.price}>
                  {convertToPersianNumber(plan.price.toLocaleString())} تومان
                </Text>
              </View>
              <View>
                <View style={styles.planHeader}>
                  {plan.default && (
                    <Text style={styles.bestValue}>پیشنهادی </Text>
                  )}
                  <Text style={styles.planLabel}>{plan.fa}</Text>
                </View>
                {plan.discount > 0 && (
                  <Text style={styles.details}>
                    {convertToPersianNumber(plan.discount.toLocaleString())}%
                    تخفیف
                  </Text>
                )}
              </View>
            </Pressable>
          ))}
        </View>

        <Pressable
          style={[
            styles.purchaseButton,
            isLoading && styles.purchaseButtonDisabled,
          ]}
          onPress={handlePurchase}
          disabled={isLoading}
        >
          <Text style={styles.purchaseText}>
            {isLoading ? "در حال پردازش..." : "پرداخت"}
          </Text>
        </Pressable>

        {/* <View style={styles.footer}>
        <Text style={styles.footerText}>Terms And Conditions</Text>
        <Text style={styles.footerDivider}> / </Text>
        <Text style={styles.footerText}>Privacy Policy</Text>
      </View> */}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  containerOuter: {
    // flex: 1,
    backgroundColor: "#1a2b4d",
    paddingTop: 50,
    height: "100%",
  },
  container: {
    flexGrow: 1,
    padding: 20,
    backgroundColor: "#1a2b4d",
    // paddingTop: 80,
  },
  title: {
    fontSize: 32,
    color: "#fff",
    textAlign: "center",
    fontFamily: "EstedadRegular",
  },
  subtitle: {
    fontSize: 13,
    color: "#ffcc80",
    textAlign: "center",
    marginBottom: 25,
    fontFamily: "EstedadRegular",
  },
  planContainer: {
    marginBottom: 20,
  },
  planBox: {
    backgroundColor: "#2c3e5b",
    borderRadius: 10,
    padding: 20,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: "transparent",
    alignItems: "center",
    justifyContent: "space-between",
    flex: 1,
    flexDirection: "row",
    minHeight: 100,
  },
  activePlan: {
    borderColor: "#f39c12",
  },
  planHeader: {
    flexDirection: "row",
    alignItems: "center",
    // marginBottom: 10,
  },
  planLabel: {
    fontSize: 18,
    fontFamily: "EstedadRegular",
    color: "#fff",
  },
  bestValue: {
    fontSize: 14,
    color: "rgb(20, 20, 20)",
    backgroundColor: "#ffcc80",
    borderRadius: 5,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 10,
    fontFamily: "EstedadRegular",
  },
  mostPopular: {
    fontSize: 12,
    color: "#fff",
    backgroundColor: "#e74c3c",
    borderRadius: 5,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 10,
  },
  price: {
    fontSize: 17,
    color: "#fff",
    fontFamily: "EstedadRegular",
  },
  period: {
    fontSize: 14,
    color: "#a9b2c3",
  },
  details: {
    fontSize: 13,
    color: "#a9b2c3",
    marginTop: 5,
    textAlign: "right",
    fontFamily: "EstedadRegular",
  },
  purchaseButton: {
    backgroundColor: "#3498db",
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
  },
  purchaseText: {
    fontSize: 18,
    color: "#fff",
    fontFamily: "EstedadRegular",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  footerText: {
    fontSize: 12,
    color: "#a9b2c3",
  },
  footerDivider: {
    fontSize: 12,
    color: "#a9b2c3",
    marginHorizontal: 5,
  },
  purchaseButtonDisabled: {
    opacity: 0.7,
  },
});

export default SubscriptionScreen;

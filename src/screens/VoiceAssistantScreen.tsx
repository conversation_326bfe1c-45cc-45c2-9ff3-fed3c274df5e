import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { useIsFocused } from '@react-navigation/native';
import * as Speech from 'expo-speech';
import VoiceAssistant from '../components/VoiceAssistant';
import { View, StyleSheet } from 'react-native';
import { llmRequest, getTranslation } from '../components/chat/chatUtils';
import { v4 as uuidv4 } from 'uuid';
import { initRecording, startRecording, stopRecording, detectSilence } from '../utils/recordingUtils';

// Constants for silence detection
const SILENCE_DURATION = 2000; // 2 seconds of silence before stopping recording
import AudioRecord from 'react-native-audio-record';

export default function VoiceAssistantScreen({ route, navigation }) {
  const [botIsSpeaking, setBotIsSpeaking] = useState<boolean | null>(null);
  const [currentResponse, setCurrentResponse] = useState("");
  const [translatedResponse, setTranslatedResponse] = useState("");
  const [chatId, setChatId] = useState('');
  const [chatHistory, setChatHistory] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [audioDataListener, setAudioDataListener] = useState(null);
  
  // Initialize chat session
  useEffect(() => {
    const initChat = async () => {
      try {
        const uuid = uuidv4();
        setChatId(uuid);
        // Initial message from assistant using FreeTalk scenario
        const response = await llmRequest(
          [{ role: "user", content: "Start a friendly conversation" }],
          uuid,
          "67ba493b70ebab475c063a79"
        );
        const translation = await getTranslation(response.message.content);
        setCurrentResponse(response.message.content);
        setTranslatedResponse(translation);
        setChatHistory([{ role: 'assistant', content: response.message.content }]);
      } catch (error) {
        console.error('Error initializing chat:', error);
      }
    };
    initChat();
  }, []);
  
  // Start recording when the assistant finishes speaking
  useEffect(() => {
    if (!botIsSpeaking && currentResponse !== "") {
      startRecordingSession();
    }
  }, [botIsSpeaking, currentResponse]);
  
  const isFocused = useIsFocused();
  
  // Hide tab bar when this screen is focused
  useEffect(() => {
    navigation.getParent()?.setOptions({ tabBarStyle: { display: 'none' } });
    return () => navigation.getParent()?.setOptions({ tabBarStyle: { display: 'flex' } });
  }, [navigation]);

  // Start speaking when the screen loads
  useEffect(() => {
    if (isFocused) {
      speakResponse(currentResponse);
    }
    return () => {
      Speech.stop();
    };
  }, [isFocused]);

  React.useEffect(() => {
    initRecording();

    // Cleanup function
    return () => {
      if (audioDataListener) {
        audioDataListener.remove();
      }
    };
  }, []);

  // Function to handle speech
  const speakResponse = (text: string) => {
    setBotIsSpeaking(true);
    Speech.speak(text, {
      language: 'en',
      rate: 0.9,
      onDone: () => {
        setBotIsSpeaking(false);
        if (onSpeechEnd) onSpeechEnd();
        // Start recording after speech ends
        startRecordingSession();
      },
      onError: (error) => {
        console.error('Speech error:', error);
        setBotIsSpeaking(false);
      },
    });
  };

  // Function to handle recording session
  const startRecordingSession = async () => {
    if (isRecording) return;
    
    try {
      // await initRecording();
      setIsRecording(true);
      
      let silenceStartTime: number | null = null;
      let userIsSpeaking = false;
      
      const listener = startRecording(silenceStartTime, botIsSpeaking);
        // const isSilent = detectSilence( 
        //   () => {
        //     if (Date.now() - silenceStartTime > SILENCE_DURATION) {
        //       handleStopRecording();
        //       silenceStartTime = null;
        //       userIsSpeaking = false;
        //     }
        //   },
        //   () => {
        //     userIsSpeaking = true;
        //     silenceStartTime = null;
        //   }
        // );
        
        // If user is speaking and then goes silent
        // if (botIsSpeaking && isSilent) {
        //   if (!silenceStartTime) {
        //     silenceStartTime = Date.now();
        //   }
        // }
      // });
      
      setAudioDataListener(listener);
    } catch (error) {
      console.error('Error starting recording:', error);
      setIsRecording(false);
    }
  };

  // Function to handle stop recording
  const handleStopRecording = async () => {
    if (!isRecording) return;
    
    try {
      setIsTranscribing(true);
      setIsRecording(false);
      
      if (audioDataListener) {
        audioDataListener.remove();
        setAudioDataListener(null);
      }
      
      const transcription = await stopRecording();
      if (transcription && transcription.trim() !== '') {
        handleMicPress(transcription);
      }
    } catch (error) {
      console.error('Error stopping recording:', error);
    } finally {
      setIsTranscribing(false);
    }
  };

  // Function to handle microphone press
  const handleMicPress = async (userMessage) => {
    if (botIsSpeaking) {
      Speech.stop();
      setBotIsSpeaking(false);
      return;
    }
    
    if (isRecording) {
      await handleStopRecording();
      return;
    }

    try {
      // Add user message to chat history
      const updatedHistory = [...chatHistory, { role: 'user', content: userMessage }];
      
      // Get response from server
      const response = await llmRequest(
        updatedHistory,
        chatId,
        'voice_assistant'
      );

      // Get translation for the response
      const translation = await getTranslation(response.message.content);
      
      // Update states
      setCurrentResponse(response.message.content);
      setTranslatedResponse(translation);
      setChatHistory([...updatedHistory, { role: 'assistant', content: response.message.content }]);
      
      // Speak the response
      speakResponse(response.message.content);
    } catch (error) {
      console.error('Error processing voice input:', error);
    }
  };

  // Function to handle end call
  const handleEndCall = () => {
    Speech.stop();
    navigation.goBack();
  };

  // Function called when speech ends
  const onSpeechEnd = () => {
    // This would typically trigger the next step in the conversation
    console.log('Speech ended');
  };

  return (
    <View style={styles.container}>
      {isFocused && <StatusBar style="light" />}
      
      <VoiceAssistant
        botIsSpeaking={botIsSpeaking}
        setBotIsSpeaking={setBotIsSpeaking}
        responseText={currentResponse}
        translatedText={translatedResponse}
        onMicPress={handleMicPress}
        onSpeechEnd={onSpeechEnd}
        onEndCall={handleEndCall}
        isUserSpeaking={isRecording}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
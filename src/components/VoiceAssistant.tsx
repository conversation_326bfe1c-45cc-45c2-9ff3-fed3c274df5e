import React, { useState, useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, ImageBackground } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import * as Speech from 'expo-speech';

interface VoiceAssistantProps {
  onSpeechEnd?: () => void;
  onMicPress?: () => void;
  isSpeaking: boolean | null;
  setIsSpeaking: (value: boolean | null) => void;
  responseText?: string;
  translatedText?: string;
  avatarSource?: any;
  onEndCall?: () => void;
  isUserSpeaking?: boolean;
}

const VoiceAssistant: React.FC<VoiceAssistantProps> = ({
  onSpeechEnd,
  onMicPress,
  isSpeaking,
  setIsSpeaking,
  responseText = "I'm based on OpenAI's GPT-3 model. It's designed to understand and generate human-like text. What about AI interests you?",
  translatedText = "من بر اساس مدل OpenAI GPT-3 ساخته شده‌ام.",
  avatarSource = require('../../assets/icons/bot100.png'),
  onEndCall,
  isUserSpeaking = false,
}) => {
  const [timer, setTimer] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  
  // Format timer as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (!isPaused) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer + 1);
      }, 1000);
    }
    
    return () => clearInterval(interval);
  }, [isPaused]);

  // Handle microphone press
  const handleMicPress = () => {
    if (onMicPress) {
      onMicPress();
    }
  };

  // Handle pause/resume
  const handlePauseResume = () => {
    setIsPaused(!isPaused);
    if (isSpeaking) {
      Speech.stop();
      setIsSpeaking(false);
    }
  };

  // Handle end call
  const handleEndCall = () => {
    if (onEndCall) {
      Speech.stop();
      setIsSpeaking(false);
      onEndCall();
    }
  };

  return (
    <LinearGradient
      colors={['#0a1535', '#1a2a5e', '#0a1535']}
      style={styles.container}
    >
      {/* Timer */}
      {/* <Text style={styles.timer}>{formatTime(timer)}</Text> */}
      
      {/* Avatar Container */}
      <View style={styles.avatarContainer}>
        {/* Main Avatar */}
        <View style={styles.avatarCircle}>
          <Image source={avatarSource} style={styles.avatar} />
        </View>
        
        {/* Speaking Indicator */}
        {isSpeaking && (
          <View style={styles.speakingBubble}>
            <View style={styles.wavesContainer}>
              <View style={[styles.wave, styles.wave1]} />
              <View style={[styles.wave, styles.wave2]} />
              <View style={[styles.wave, styles.wave3]} />
            </View>
          </View>
        )}
        
        {/* User Speaking Indicator */}
        {isUserSpeaking && (
          <View style={styles.userSpeakingBubble}>
            <View style={styles.userWavesContainer}>
              <View style={[styles.userWave, styles.userWave1]} />
              <View style={[styles.userWave, styles.userWave2]} />
              <View style={[styles.userWave, styles.userWave3]} />
            </View>
          </View>
        )}
      </View>
      
      {/* Response Text */}
      <View style={styles.textContainer}>
        <Text style={styles.responseText}>{responseText}</Text>
        <Text style={styles.translatedText}>{translatedText}</Text>
      </View>
      
      {/* Tap to interrupt */}
      <Text style={styles.interruptText}>Tap to interrupt</Text>
      
      {/* Control Buttons */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity style={styles.controlButton} onPress={handlePauseResume}>
          {isPaused ? (
            <Ionicons name="play" size={30} color="#fff" />
          ) : (
            <Ionicons name="pause" size={30} color="#fff" />
          )}
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.controlButton, styles.primaryButton]} onPress={handleEndCall}>
          <MaterialIcons name="call-end" size={30} color="#fff" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.controlButton} >
          <Ionicons name="settings-outline" size={30} color="#fff" />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  userSpeakingBubble: {
    position: 'absolute',
    bottom: -20,
    alignSelf: 'center',
    backgroundColor: '#4CAF50',
    padding: 10,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userWavesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 20,
  },
  userWave: {
    width: 3,
    marginHorizontal: 1,
    backgroundColor: '#ffffff',
    borderRadius: 2,
  },
  userWave1: {
    height: 10,
    opacity: 0.4,
  },
  userWave2: {
    height: 16,
    opacity: 0.7,
  },
  userWave3: {
    height: 12,
    opacity: 0.5,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  timer: {
    color: '#4caf50',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
  },
  avatarContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 30,
  },
  avatarCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  avatar: {
    width: '100%',
    height: '100%',
    borderRadius: 100,
  },
  speakingBubble: {
    position: 'absolute',
    top: 20,
    right: -20,
    backgroundColor: '#4285F4',
    borderRadius: 25,
    padding: 10,
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  wavesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 30,
  },
  wave: {
    width: 6,
    backgroundColor: 'white',
    borderRadius: 3,
  },
  wave1: {
    height: 10,
    animationName: 'wave',
    animationDuration: '1s',
    animationIterationCount: 'infinite',
  },
  wave2: {
    height: 16,
    animationName: 'wave',
    animationDuration: '1s',
    animationDelay: '0.2s',
    animationIterationCount: 'infinite',
  },
  wave3: {
    height: 10,
    animationName: 'wave',
    animationDuration: '1s',
    animationDelay: '0.4s',
    animationIterationCount: 'infinite',
  },
  textContainer: {
    alignItems: 'center',
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  responseText: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 10,
  },
  translatedText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Estedad-Regular',
  },
  interruptText: {
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: 14,
    marginBottom: 20,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#F44336',
  },
});

export default VoiceAssistant;
import RequestHelper from "../utils/requestHelper";
import React, { useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Pressable,
  Image,
  ScrollView,
} from "react-native";
import {
  MaterialCommunityIcons,
  FontAwesome5,
  Ionicons,
} from "@expo/vector-icons";
import constants from "../utils/constants";
import Toast from "react-native-toast-message";
import { useAppDispatch } from "../store/hooks";
import { updateCoins } from "../store/slices/authSlice";
import styles from "../styles/AssessmentModalStyle";

interface TranscriptionItem {
  type: string;
  text: string;
  isTopic: boolean;
}

interface RewardsData {
  pointsEarned: number;
  gemsEarned: number;
  totalPoints: number;
  totalGems: number;
  gameLevel: number;
  levelProgress: number;
}

interface AssessmentModalProps {
  isModalVisible: boolean;
  setIsModalVisible: (visible: boolean) => void;
  chatId: string;
  topicItem: {
    _id: string;
  };
  scores: any;
  setScores: (scores: any) => void;
  transcriptionResult: TranscriptionItem[];
  navigation?: any; // Navigation prop for redirecting after assessment
}

const AssessmentModal = ({
  isModalVisible,
  setIsModalVisible,
  chatId,
  topicItem,
  scores,
  setScores,
  transcriptionResult,
  navigation,
}: AssessmentModalProps) => {
  const [rewards, setRewards] = React.useState<RewardsData | null>(null);
  const [page, setPage] = React.useState(0); // 0: ScoreCard, 1: RewardsCard
  const dispatch = useAppDispatch();
  const getUserAssessment = async () => {
    try {
      // Check if there are enough messages for assessment
      const userMessages = transcriptionResult.filter(
        (item) => item.type === "req" && !item.isTopic
      );

      if (userMessages.length < constants.numberOfMessagesForAssessment) {
        setIsModalVisible(false);
        Toast.show({
          text1: "حداقل 7 پیام برای ارزیابی ارسال کنید",
          type: "error",
        });
        return;
      }

      const response = await RequestHelper(
        "post",
        "/ai/assessment",
        {
          chatId,
          scenarioId: topicItem._id,
        },
        null,
        null,
        true
      );
      setScores(response.scores);
      setRewards(response.rewards);
      // Update coins in the store if gems were earned
      if (response.rewards && response.rewards.gemsEarned > 0) {
        dispatch(updateCoins(response.rewards.totalGems));
      }
    } catch (error) {
      setIsModalVisible(false);
    }
  };

  useEffect(() => {
    if (isModalVisible) {
      setScores(null);
      setPage(0); // Reset to first page when modal opens
      getUserAssessment();
    }
  }, [isModalVisible]);

  const ScoreCard = () => {
    return (
      <View style={styles.container}>
        <ScoreItem label="گرامر" score={scores?.Grammar} />
        <ScoreItem label="واژگان" score={scores?.Vocabulary} />
        <ScoreItem label="مکالمه" score={scores?.Conversation} />
        <View style={styles.separator} />
        <View style={styles.totalScoreContainer}>
          <Text style={styles.totalLabel}>امتیاز کل</Text>
          <Text style={styles.totalScore}>{scores?.average}%</Text>
        </View>
      </View>
    );
  };

  const RewardsCard = () => {
    if (!rewards) return null;

    const {
      pointsEarned,
      gemsEarned,
      totalPoints,
      totalGems,
      gameLevel,
      levelProgress,
    } = rewards;

    return (
      <View style={styles.rewardsContainer}>
        <Text style={styles.rewardsTitle}>پاداش‌ها</Text>

        {/* Points earned */}
        {pointsEarned > 0 && (
          <View style={styles.rewardItem}>
            <View style={styles.rewardIconContainer}>
              <FontAwesome5 name="star" size={18} color="#FFD700" />
            </View>
            <Text style={styles.rewardLabel}>امتیاز کسب شده</Text>
            <Text style={styles.rewardValue}>+{pointsEarned}</Text>
          </View>
        )}

        {/* Gems earned */}
        {gemsEarned > 0 && (
          <View style={styles.rewardItem}>
            <View style={styles.rewardIconContainer}>
              <Image
                source={require("../../assets/icons/dollar.png")}
                style={{ width: 18, height: 18 }}
              />
            </View>
            <Text style={styles.rewardLabel}>سکه کسب شده</Text>
            <Text style={styles.rewardValue}>+{gemsEarned}</Text>
          </View>
        )}

        <View style={styles.separator} />

        {/* Total stats */}
        <View style={styles.rewardTotalsContainer}>
          {/* Game level */}
          <View style={styles.rewardTotalItem}>
            <Ionicons name="trophy" size={20} color="#5A4FCF" />
            <Text style={styles.rewardTotalLabel}>سطح</Text>
            <Text style={styles.rewardTotalValue}>{gameLevel}</Text>
          </View>

          {/* Total points */}
          <View style={styles.rewardTotalItem}>
            <FontAwesome5 name="star" size={18} color="#FFD700" />
            <Text style={styles.rewardTotalLabel}>امتیاز کل</Text>
            <Text style={styles.rewardTotalValue}>{totalPoints}</Text>
          </View>

          {/* Total gems */}
          <View style={styles.rewardTotalItem}>
            <Image
              source={require("../../assets/icons/dollar.png")}
              style={{ width: 18, height: 18 }}
            />
            <Text style={styles.rewardTotalLabel}>سکه کل</Text>
            <Text style={styles.rewardTotalValue}>{totalGems}</Text>
          </View>
        </View>

        {/* Level progress */}
        <View style={styles.levelProgressContainer}>
          <Text style={styles.levelProgressLabel}>پیشرفت سطح</Text>
          <View style={styles.levelProgressBarContainer}>
            <View
              style={[styles.levelProgressBar, { width: `${levelProgress}%` }]}
            />
          </View>
          <Text style={styles.levelProgressValue}>{levelProgress}%</Text>
        </View>
      </View>
    );
  };

  const ScoreItem = ({ label, score }: { label: string; score: number }) => {
    return (
      <View style={styles.scoreItem}>
        <View
          style={{
            flexDirection: "row",
            width: "100%",
            justifyContent: "space-between",
            marginBottom: 5,
          }}
        >
          <Text style={styles.score}>{score}%</Text>
          <Text style={styles.label}>{label}</Text>
        </View>
        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { width: `${score}%` }]} />
        </View>
      </View>
    );
  };

  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      {scores?.average && (
      <Modal
        transparent={true}
        visible={isModalVisible}
        animationType="fade"
        onRequestClose={() => {
          setIsModalVisible(!isModalVisible);
        }}
      >
        <View style={styles.modalBackground}>
          <View style={styles.alertContainer}>
            {/* <View style={styles.modalHeader}>
              <Pressable
                hitSlop={{ top: 30, bottom: 30, left: 30, right: 30 }}
                onPress={() => {
                  setIsModalVisible(!isModalVisible);
                  // Navigate back to topics screen if navigation is available
                  if (navigation) {
                    setTimeout(() => {
                      navigation.navigate("topic", {
                        refresh: true,
                        lastTopicId: topicItem._id,
                      });
                    }, 300); // Small delay to ensure modal is closed first
                  }
                }}
              >
                <MaterialCommunityIcons
                  name="close"
                  size={22}
                  color="white"
                  style={{
                    marginLeft: 5,
                  }}
                />
              </Pressable>
              <Text
                style={{
                  color: "white",
                  fontFamily: "EstedadRegular",
                  fontSize: 18,
                  marginRight: 10,
                  textAlign: "center",
                }}
              >
                ارزیابی
              </Text>
            </View> */}
            <ScrollView style={styles.scores}>
              {page === 0 && <RewardsCard /> }
              {page === 1 && <ScoreCard />}
            </ScrollView>
            {/* Navigation Buttons */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
                paddingHorizontal: 20,
                marginBottom: 15,
              }}
            >
              {page === 1 && (
                <TouchableOpacity
                  onPress={() => setPage(0)}
                  style={[
                    styles.alertButton,
                    { width: 100, backgroundColor: "#e0e0e0" },
                  ]}
                >
                  <Text style={[styles.buttonText, { color: "#333" }]}>
                    قبلی
                  </Text>
                </TouchableOpacity>
              )}
              {page === 0 && rewards && (
                <TouchableOpacity
                  onPress={() => setPage(1)}
                  style={[styles.alertButton, { width: 100 }]}
                >
                  <Text style={styles.buttonText}>بعدی</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </Modal>
      )}
    </View>
  );
};

export default AssessmentModal;

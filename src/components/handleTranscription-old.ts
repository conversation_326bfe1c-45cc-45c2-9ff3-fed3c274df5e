import React, { useState, useEffect } from "react";
import RNFS from "react-native-fs";
import RequestHelper from "../utils/requestHelper";
import { checkIfReachedLastMessage } from "./chat/chatActions";
import llmResponeTrimer from "../utils/llmResponeTrimer";
interface TranscriptionItem {
  key: number;
  type: "req" | "res" | "loading" | "loadingReq" | "loadingRes";
  text: string;
  isTopic: boolean;
  correctedText?: string;
  _id?: string;
}

export const handleRecordingComplete = async (
  setHasReachedDailyLimit: (reached: boolean) => void,
  transcriptionResult: TranscriptionItem[], 
  setTranscriptionResult: (result: TranscriptionItem[]) => void,
  chatId: string,
  uri: string,
  setTextSubmitted: (submitted: boolean) => void,
  topicItem,
  setShowCongratulations: (show: boolean) => void,
  level: string, 
  setPlaySpeech: (play: boolean) => void, 
) => {
  const loadingKeyReq = Date.now() + Math.floor(Math.random() * 1000);
  let currentTranscriptionResult = [...transcriptionResult]; 

  currentTranscriptionResult.push({
    key: loadingKeyReq,
    type: "loadingReq",
    text: "",
    isTopic: false,
  });
  setTranscriptionResult(currentTranscriptionResult);

  try {
    const mp3FormData = prepareTranscriptionRequestData(chatId, uri);
    const result = await sendTranscriptionRequest(mp3FormData);
    const { userTranscription, aiResponse, hasReachedDailyLimit, messageId } =
      result;

    if (hasReachedDailyLimit) {
      setHasReachedDailyLimit(true);
    }

    // Update the state using the initial state *before* the loading indicator was added
    updateTranscriptionWithResult(
      transcriptionResult,
      userTranscription,
      aiResponse,
      setTranscriptionResult,
      loadingKeyReq,
      messageId,
      setTextSubmitted,
      level
    );

    // TODO: Add logic to play speech for aiResponse if needed
    setPlaySpeech(true);

    checkIfReachedLastMessage(
      topicItem,
      transcriptionResult,
      setShowCongratulations
    );
  } catch (error) {
    // Handle error using the state *before* the loading indicator was added
    handleTranscriptionError(
      error,
      transcriptionResult,
      loadingKeyReq,
      setTranscriptionResult
    );
  } finally {
    // Optional: Cleanup MP3 file
    // const filePathMp3 = RNFS.DocumentDirectoryPath + "/test.mp3";
    // RNFS.unlink(filePathMp3).catch(err => console.log("Error deleting temp mp3:", err));
  }
};

// --- Helper Functions for handleRecordingComplete ---
/**
 * Prepares the FormData for the transcription request.
 */
const prepareTranscriptionRequestData = (
  chatId: string,
  uri: string
): FormData => {
  // const filePathMp3 = RNFS.DocumentDirectoryPath + "/audio/.mp3";
  const fileUri = `file://${uri}`;
  const mp3FormData = new FormData();
  mp3FormData.append("file", {
    uri: fileUri,
    type: "audio/m4a",
    name: "test.mp3",
  } as any); // Cast to 'any' for RN FormData compatibility
  mp3FormData.append("chatId", chatId);
  return mp3FormData;
};

/**
 * Sends the transcription request to the backend with retry logic.
 */
const sendTranscriptionRequest = async (
  formData: FormData,
  retries = 2,
  delay = 1000
): Promise<any> => {
  for (let i = 0; i < retries; i++) {
    try {
      // Attempt the request
      const result = await RequestHelper("post", "/transcribe/llm", formData, {
        "Content-Type": "multipart/form-data",
      });
      return result; // Success, return the result
    } catch (error: any) {
      // Check if it's a network error or the last attempt
      // Note: The specific error condition for a "network error" might depend
      // on how RequestHelper throws errors. Adjust the condition if needed.
      const isNetworkError =
        error.message?.toLowerCase().includes("network Error") ||
        error.message?.toLowerCase().includes("timeout"); // Example check

      if (isNetworkError && i < retries - 1) {
        console.log(
          `Network error detected. Retrying attempt ${i + 1} of ${retries}...`
        );
        // Wait for the specified delay before retrying
        await new Promise((resolve) => setTimeout(resolve, delay));
      } else {
        // If it's not a network error or it's the last retry, re-throw the error
        console.error(
          `Transcription request failed after ${
            i + 1
          } attempts or due to non-network error.`
        );
        throw error;
      }
    }
  }
  // This line should theoretically not be reached if retries > 0,
  // but included for completeness and to satisfy TypeScript return type.
  throw new Error("Transcription request failed after all retries.");
};

/**
 * Updates the transcription result state after a successful API call.
 */
const updateTranscriptionWithResult = (
  initialTranscriptionResult: TranscriptionItem[],
  userTranscription: string,
  aiResponse: string,
  setTranscriptionResult: (result: TranscriptionItem[]) => void,
  loadingKeyReq: number,
  messageId: string,
  setTextSubmitted: (submitted: boolean) => void,
  level: string
) => {
  // Filter out any previous loading indicators
  let resultWithoutLoading = initialTranscriptionResult.filter(
    (item: TranscriptionItem) => !item.type.includes("loading")
  );

  // Create user transcription (req) message object
  setTextSubmitted(true);
  const userMessageObject: TranscriptionItem = {
    key: loadingKeyReq,
    type: "req",
    text: userTranscription,
    isTopic: false,
    // _id: messageId, // Use the same key for consistency
  };

  setTranscriptionResult([...resultWithoutLoading, userMessageObject]);

  setTimeout(() => {
    const loadingKeyRes = Date.now() + Math.floor(Math.random() * 1000);
    const loadingResObject: TranscriptionItem = {
      key: loadingKeyRes,
      type: "loadingRes",
      text: "",
      isTopic: false,
    };
    setTranscriptionResult([
      ...resultWithoutLoading,
      userMessageObject,
      loadingResObject,
    ]);

    let trimmedSentence = llmResponeTrimer(aiResponse, level);

    // Create AI response (res) message object
    setTimeout(() => {
      const aiResponseMessageObject: TranscriptionItem = {
        key: loadingKeyRes,
        type: "res",
        text: trimmedSentence,
        isTopic: false,
      };

      // Update state with both new messages
      setTranscriptionResult([
        ...resultWithoutLoading,
        userMessageObject,
        aiResponseMessageObject,
      ]);
    }, 500);
  }, 500);
};

/**
 * Handles errors during the transcription process.
 */
const handleTranscriptionError = (
  error: any,
  transcriptionResult: TranscriptionItem[],
  loadingKeyReq: number, // Pass the specific loading key to remove
  setTranscriptionResult: (result: TranscriptionItem[]) => void
) => {
  console.error("Transcription error:", error);
  // Remove the specific loading indicator ('loadingReq') on error
  const errorResult = transcriptionResult.filter(
    (item: TranscriptionItem) => item.key !== loadingKeyReq
  );
  setTranscriptionResult(errorResult);
};

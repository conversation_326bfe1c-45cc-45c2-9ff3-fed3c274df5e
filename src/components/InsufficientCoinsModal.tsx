import React from "react";
import { Modal, View, Text, TouchableOpacity, StyleSheet, Image } from "react-native";
import { convertToPersianNumber } from "../utils/helpers";

interface InsufficientCoinsModalProps {
  visible: boolean;
  requiredCoins: number;
  onClose: () => void;
  onSubscribe: () => void;
}

const InsufficientCoinsModal = ({
  visible,
  requiredCoins,
  onClose,
  onSubscribe,
}: InsufficientCoinsModalProps) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Image source={require("../../assets/icons/gem.png")} style={styles.icon} />
          <Text style={styles.title}>الماس ناکافی</Text>
          <Text style={styles.message}>
             برای شروع این درس به {convertToPersianNumber(requiredCoins.toString())} الماس نیاز داری. با خرید اشتراک می‌تونی بدون محدودیت تمرین‌ها رو انجام بدی
          </Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelText}>بعدا</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.subscribeButton} onPress={onSubscribe}>
              <Text style={styles.subscribeText}>خرید اشتراک</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.4)",
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    width: 320,
    elevation: 8,
  },
  icon: {
    width: 40,
    height: 40,
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    color: "#d303fc",
    marginBottom: 8,
    fontFamily: "EstedadRegular",
  },
  message: {
    fontSize: 16,
    color: "#333",
    textAlign: "center",
    marginBottom: 20,
    fontFamily: "EstedadRegular",
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#eee",
    borderRadius: 8,
    paddingVertical: 10,
    marginRight: 8,
    alignItems: "center",
  },
  subscribeButton: {
    flex: 1,
    backgroundColor: "#4A84F0",
    borderRadius: 8,
    paddingVertical: 10,
    marginLeft: 8,
    alignItems: "center",
  },
  cancelText: {
    color: "#333",
    fontSize: 16,
    fontFamily: "EstedadRegular",
  },
  subscribeText: {
    color: "#fff",
    fontSize: 16,
    fontFamily: "EstedadRegular",
  },
});

export default InsufficientCoinsModal;

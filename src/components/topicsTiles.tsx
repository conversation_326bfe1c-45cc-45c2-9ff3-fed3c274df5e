import { Text, TouchableHighlight, View } from "react-native";

const GradientButton = ({ title, onPress }) => (
  <TouchableHighlight
    style={{
      width: 100,
      padding: 7,
      marginHorizontal: 5,
      marginVertical: 3,
      borderRadius: 5,
      backgroundColor: "#006bbd",
      justifyContent: "center",
      alignItems: "center",
    }}
    onPress={onPress}
    underlayColor="#442C96"
  >
    <Text style={{ color: "#fff", fontSize: 14, textAlign: "center" }}>
      {title}
    </Text>
  </TouchableHighlight>
);

const MyComponent = ({ startTopic }) => {
  
  return (
    <>
      <View
        style={{
          flexDirection: "row",
          flexWrap: "wrap",
        }}
      >
        {topics.map((topic, index) => (
          <GradientButton
            key={index}
            title={topic.key}
            onPress={() => startTopic(topic.value)}
          />
        ))}
      </View>
    </>
  );
};

export default MyComponent;

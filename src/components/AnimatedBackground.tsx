import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions, Image, Easing } from 'react-native';

interface AnimatedBackgroundProps {
  children?: React.ReactNode;
}

const { width, height } = Dimensions.get('window');

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({ children }) => {
  // Create animated values for position
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(1.0)).current;
  const rotate = useRef(new Animated.Value(0)).current;

  // Function to start the animation
  const startAnimation = () => {
    // Reset animation values
    translateX.setValue(0);
    translateY.setValue(0);
    scale.setValue(1.0);
    rotate.setValue(0);

    // Create a sequence of animations
    Animated.loop(
      Animated.sequence([
        // Move slightly in different directions and scale
        Animated.parallel([
          Animated.timing(translateX, {
            toValue: 15,
            duration: 20000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: -15,
            duration: 24000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1.15,
            duration: 30000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(rotate, {
            toValue: 0.02,
            duration: 40000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
        ]),
        // Move back in opposite directions
        Animated.parallel([
          Animated.timing(translateX, {
            toValue: -15,
            duration: 20000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: 15,
            duration: 24000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1.0,
            duration: 30000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(rotate, {
            toValue: -0.02,
            duration: 40000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
        ]),
      ]),
      { iterations: -1 } // Loop indefinitely
    ).start();
  };

  // Start animation when component mounts
  useEffect(() => {
    startAnimation();
    return () => {
      // Clean up animations if needed
      translateX.stopAnimation();
      translateY.stopAnimation();
      scale.stopAnimation();
      rotate.stopAnimation();
    };
  }, []);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.backgroundContainer,
          {
            transform: [
              { translateX },
              { translateY },
              { scale },
              { rotate: rotate.interpolate({
                inputRange: [-1, 1],
                outputRange: ['-5deg', '5deg']
              }) },
            ],
          },
        ]}
      >
        {/* <Image
          source={require('../../assets/gamification/Yellow_and_Pink_Festive_Illustrative_Congratulation_And_Award_Instagram.png')}
          style={styles.backgroundImage}
          resizeMode="cover"
        /> */}
      </Animated.View>
      <View style={styles.contentContainer}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: width + 60, // Add extra width to allow for movement
    height: height + 60, // Add extra height to allow for movement
    marginLeft: -30,
    marginTop: -30,
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
  },
  contentContainer: {
    flex: 1,
    zIndex: 1,
  },
});

export default AnimatedBackground;

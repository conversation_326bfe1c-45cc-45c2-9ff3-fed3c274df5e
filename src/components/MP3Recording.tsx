import * as React from "react";
import {
  Text,
  View,
  StyleSheet,
  Button,
  Pressable,
  Image,
  ActivityIndicator,
} from "react-native";
// Removed PermissionsAndroid import
import { FontAwesome } from "@expo/vector-icons";
// Removed RNFS import (expo-av handles file paths)
import { Audio } from "expo-av"; // Import Audio from expo-av

// Define an interface for the component props
interface MP3RecordingProps {
  Speech: { stop: () => void }; // Assuming Speech has a stop method
  setIsSpeaking: React.Dispatch<React.SetStateAction<any>>; // Type for the state setter
  onRecordingComplete: (uri: string) => void; // Callback function type
}

export default function MP3Recording({ Speech, setIsSpeaking, onRecordingComplete }: MP3RecordingProps) {
  const [isRecording, setIsRecording] = React.useState<boolean>(false); // Renamed for clarity
  const [transcribing, setTranscribing] = React.useState<boolean>(false);
  const recordingRef = React.useRef<Audio.Recording | null>(null); // Ref to hold the recording object
  const stopTimeoutRef = React.useRef<NodeJS.Timeout | null>(null); // <-- Add this ref

  // Use expo-av permission handling
  async function checkAndRequestMicrophonePermission() {
    const permission = await Audio.getPermissionsAsync();
    if (permission.granted) {
      return true;
    }
    const response = await Audio.requestPermissionsAsync();
    return response.granted;
  }

  async function startRecording() {
    Speech.stop();
    setIsSpeaking(null);
    const hasPermission = await checkAndRequestMicrophonePermission();
    if (!hasPermission) {
      console.error("No microphone permission granted");
      return;
    }

    try {
      // Configure audio mode for recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording } = await Audio.Recording.createAsync(
         Audio.RecordingOptionsPresets.HIGH_QUALITY // Use a preset for simplicity
         // For more control, you can define specific options:
         // {
         //   android: {
         //     extension: '.mp3', // Specify mp3 extension
         //     outputFormat: Audio.AndroidOutputFormat.MPEG_4, // MPEG_4 container often used for mp3
         //     audioEncoder: Audio.AndroidAudioEncoder.AAC, // AAC is common, check if mp3 is directly supported or if conversion is needed post-recording
         //     sampleRate: 44100,
         //     numberOfChannels: 1,
         //     bitRate: 128000,
         //   },
         //   ios: {
         //     extension: '.mp3', // Specify mp3 extension
         //     outputFormat: Audio.IOSOutputFormat.MPEG4AAC, // Similar to Android, check direct mp3 support
         //     audioQuality: Audio.IOSAudioQuality.HIGH,
         //     sampleRate: 44100,
         //     numberOfChannels: 1,
         //     bitRate: 128000,
         //     linearPCMBitDepth: 16,
         //     linearPCMIsBigEndian: false,
         //     linearPCMIsFloat: false,
         //   },
         // }
      );
      recordingRef.current = recording;
      setIsRecording(true);

      // Set a timeout to auto-stop after 30 seconds
      stopTimeoutRef.current = setTimeout(() => {
        stopRecording();
      }, 30000);
    } catch (error) {
      console.error("Error starting recording:", error);
      setIsRecording(false); // Reset state on error
    }
  }

  async function stopRecording() {
    if (!recordingRef.current) {
      console.log("No active recording to stop.");
      return;
    }

    // Clear the auto-stop timeout if it exists
    if (stopTimeoutRef.current) {
      clearTimeout(stopTimeoutRef.current);
      stopTimeoutRef.current = null;
    }

    setTranscribing(true); // Indicate transcription process starts
    setIsRecording(false); // Update UI state

    try {
      await recordingRef.current.stopAndUnloadAsync();
      const uri = recordingRef.current.getURI(); // Get the URI of the recorded file
      recordingRef.current = null; // Clear the ref

      // Reset audio mode (optional, depends on app needs)
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
      });

      if (uri && onRecordingComplete) {
        onRecordingComplete(uri); // Pass the URI to the callback
      }
    } catch (error) {
      console.error("Error stopping recording:", error);
    } finally {
      setTranscribing(false); // Transcription process finished (or failed)
    }
  }

  React.useEffect(() => {
    // Cleanup function
    return () => {
      if (recordingRef.current) {
        recordingRef.current.stopAndUnloadAsync(); // Ensure recording is stopped and unloaded
        recordingRef.current = null;
      }
      // Clear timeout on unmount
      if (stopTimeoutRef.current) {
        clearTimeout(stopTimeoutRef.current);
        stopTimeoutRef.current = null;
      }
    };
  }, []); // Empty dependency array ensures this runs only on mount and unmount

  return (
    <Pressable
      onPress={isRecording ? stopRecording : startRecording} // Use updated state variable
      style={{ height: 58, marginLeft: 10 }}
    >
      {transcribing ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        <FontAwesome
          name={isRecording ? "stop-circle" : "microphone"} // Use updated state variable
          size={36}
          color="#13538f"
        />
      )}
    </Pressable>
  );
}

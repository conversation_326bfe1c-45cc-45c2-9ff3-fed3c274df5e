import { View, TextInput, Text, Pressable } from "react-native";
import { FontAwesome6 } from "@expo/vector-icons/";
import { useAppSelector } from "../store/hooks";
import styles from "../styles/chatStyle";
import ScoreResult from "./ScoreResult";
import Recording from "./Recording";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useEffect, useState } from "react";
import MP3Recording from "./MP3Recording";

interface TopicItem {
  _id: string;
  title: string;
  scenario: string;
}

interface InputComponentProps {
  inputValue: string;
  inputHeight: number;
  setInputHeight: (height: number) => void;
  setInputValue: (value: string) => void;
  Speech: any;
  setIsSpeaking: (index: number | null) => void;
  submit: () => void;
  scores: any;
  topicItem: TopicItem;
  hasReachedDailyLimit: boolean;
  navigation: any;
  recordingComplete: () => void;
  approvedNewChat: boolean;
}

const DailyLimitMessage = ({ navigation }: { navigation: any }) => {
  return (
    <View
      style={{
        backgroundColor: "#fff3cd",
        borderColor: "#ffeeba",
        borderWidth: 1,
        padding: 15,
        marginHorizontal: 10,
        marginBottom: 10,
        borderRadius: 5,
      }}
    >
      <Text
        style={{
          color: "#856404",
          marginBottom: 10,
          fontFamily: "EstedadRegular",
        }}
      >
        شما به حد مجاز رایگان روزانه خود رسیده‌اید. لطفاً برای ادامه استفاده از
        ویژگی گفتگوی ازاد مشترک شوید.{" "}
      </Text>
      <Pressable
        onPress={() =>
          navigation.navigate("profileTab", { screen: "subscription" })
        }
        style={{
          backgroundColor: "#607afb",
          padding: 10,
          borderRadius: 5,
          alignItems: "center",
        }}
      >
        <Text
          style={{ color: "white", fontFamily: "EstedadRegular", fontSize: 15 }}
        >
          خرید اشتراک
        </Text>
      </Pressable>
    </View>
  );
};

const InputComponent = ({
  inputValue,
  inputHeight,
  setInputHeight,
  setInputValue,
  Speech,
  setIsSpeaking,
  submit,
  scores,
  topicItem,
  hasReachedDailyLimit,
  navigation,
  recordingComplete,
  approvedNewChat,
}: InputComponentProps) => {
  const { isPremium } = useAppSelector((state) => state.auth);
  const [globalDailyLimit, setGlobalDailyLimit] = useState(false);

  // Check for daily limit in AsyncStorage when component mounts
  useEffect(() => {
    const checkDailyLimit = async () => {
      try {
        const limitData = await AsyncStorage.getItem("dailyLimitData");
        if (limitData) {
          const { timestamp } = JSON.parse(limitData);
          const currentTime = new Date().getTime();
          const threeHoursInMs = 3 * 60 * 60 * 1000;

          // Check if 3 hours have passed since the limit was reached
          if (currentTime - timestamp < threeHoursInMs) {
            setGlobalDailyLimit(true);
          } else {
            // Reset the limit if 3 hours have passed
            await AsyncStorage.removeItem("dailyLimitData");
          }
        }
      } catch (error) {
        console.log("Error checking daily limit:", error);
      }
    };

    checkDailyLimit();
  }, []);

  // Update AsyncStorage when hasReachedDailyLimit changes
  useEffect(() => {
    if (hasReachedDailyLimit && !isPremium) {
      const storeDailyLimit = async () => {
        try {
          const limitData = {
            timestamp: new Date().getTime(),
          };
          await AsyncStorage.setItem(
            "dailyLimitData",
            JSON.stringify(limitData)
          );
          setGlobalDailyLimit(true);
        } catch (error) {
          console.log("Error storing daily limit:", error);
        }
      };

      storeDailyLimit();
    }
  }, [hasReachedDailyLimit, isPremium]);
  const [localScores, setLocalScores] = useState(scores);

  useEffect(() => {
    if ( approvedNewChat) {
      setLocalScores(null);
    }
  }, [ approvedNewChat]);

  useEffect(() => {
    setLocalScores(scores);
  }, [scores]);

  if (
    localScores?.average &&
    topicItem.title !== "free talk" &&
    !approvedNewChat
  ) {
    return <ScoreResult scores={localScores} />;
  } else if (
    (hasReachedDailyLimit || globalDailyLimit) &&
    !isPremium 
    && topicItem.title === "free talk"
  ) {
    return <DailyLimitMessage navigation={navigation} />;
  } else {
    return (
      <View
        style={{
          height: 70,
          flexDirection: "row",
          justifyContent: "space-around",
          alignItems: "flex-end",
          marginTop: 10,
        }}
      >
        <View style={styles.inputContainer}>
          <TextInput
            multiline={true}
            placeholder="Message"
            textAlignVertical="center"
            value={inputValue}
            onChangeText={setInputValue}
            onSubmitEditing={(event) => {
              submit();
              setInputHeight(60);
            }}
            style={[
              styles.input,
              {
                height: Math.min(inputHeight, 120),
                paddingTop: inputHeight > 80 ? 10 : 0,
                paddingBottom: inputHeight > 80 ? 10 : 0,
              },
            ]}
            onContentSizeChange={(event) => {
              const height = event.nativeEvent.contentSize.height;
              setInputHeight(height > 80 ? height : 60);
            }}
          />
          <FontAwesome6
            onPress={() => {
              submit();
              setInputHeight(60);
            }}
            style={{ paddingHorizontal: "2%" }}
            name="circle-up"
            size={26}
            color={inputValue ? "#607afb" : "#ced3e0"}
          />
        </View>
        {/* <Recording setIsSpeaking={setIsSpeaking} Speech={Speech} /> */}
        <MP3Recording
          setIsSpeaking={setIsSpeaking}
          Speech={Speech}
          onRecordingComplete={recordingComplete}
        />
      </View>
    );
  }
};

export default InputComponent;

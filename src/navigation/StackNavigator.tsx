import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import {
  Entypo,
  MaterialIcons,
  Ionicons,
  Octicons,
  FontAwesome6,
} from "@expo/vector-icons/";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import { initNewChat } from "../store/slices/modelSlice";

import TopicsScreen from "../screens/Topics";
import ChatScreen from "../screens/Chat";
import FreeTalk from "../screens/FreeTalk";
import HomeScreen from "../screens/Home";
import ProfileScreen from "../screens/Profile";
import SubscriptionScreen from "../screens/Subscription";
import VoiceAssistantScreen from "../screens/VoiceAssistantScreen";
import GamificationScreen from "../screens/GamificationStories";
import DropdownComponent from "../components/levelSelector";
import {
  DeviceEventEmitter,
  Pressable,
  Text,
  View,
  TouchableOpacity,
  Image,
} from "react-native";

const Stack = createNativeStackNavigator();

export const HomeStack = () => {
  const dispatch = useAppDispatch();
  const { coins } = useAppSelector((state) => state.auth);
  // const Gem = () =>
  //   coins > 0 && (
  //     <>
  //       <Image
  //         source={require("../../assets/icons/gem.png")}
  //         style={{ width: 25, height: 25, marginRight: 4 }}
  //       />
  //       <Text
  //         style={{
  //           fontSize: 16,
  //           fontFamily: "EstedadRegular",
  //           color: "gold",
  //           marginRight: 25,
  //         }}
  //       >
  //         {coins}
  //       </Text>
  //     </>
  //   );
  return (
    <Stack.Navigator initialRouteName={"homeScreen"}>
      <Stack.Screen
        name="homeScreen"
        component={HomeScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="topic"
        component={TopicsScreen}
        options={({ navigation }) => ({
          headerTitle: "",
          headerRight: () => <DropdownComponent />,
          headerLeft: () => (
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                paddingLeft: 8,
                paddingVertical: 8,
                paddingRight: 20,
              }}
            >
              <Ionicons name="chevron-back" size={28} color="#000" />
            </TouchableOpacity>
          ),
          // When using a custom headerLeft, we need to hide the default back button
          headerBackVisible: false,
        })}
      />
      <Stack.Screen
        name="chat"
        component={ChatScreen}
        initialParams={{ topic: null }}
        options={{
          title: "Chat",
          headerTitleStyle: {
            fontSize: 16,
          },
          headerTitleAlign: "center",
        }}
      />
      <Stack.Screen
        name="freeTalk"
        component={FreeTalk}
        options={({ navigation }) => ({
          title: "Free Talk",
          headerStyle: {
            backgroundColor: "#4a84f0",
          },
          headerTitleAlign: "center",
          headerTitle: "گفتگوی آزاد",
          headerTitleStyle: {
            backgroundColor: "#4a84f0",
            fontFamily: "EstedadRegular",
            fontSize: 18,
          },
          //  () => (
          //   <Pressable
          //     onPress={() => {
          //       navigation.navigate("assessment", {
          //         chatId: "67ba493b70ebab475c063a79",
          //         topicItem: {
          //           _id: "67ba493b70ebab475c063a79"
          //         },
          //         transcriptionResult: navigation.getState().routes.find(route => route.name === "freeTalk")?.params?.transcriptionResult || []
          //       });
          //     }}
          //     style={{
          //       flexDirection: "row",
          //     }}
          //   >
          //     <Text
          //       style={{
          //         fontSize: 16,
          //         fontFamily: "EstedadRegular",
          //         marginRight: 3,
          //         color: "#fff",
          //       }}
          //     >
          //       ارزیابی
          //     </Text>
          //     <MaterialIcons name="assessment" size={24} color="#fff" />
          //   </Pressable>
          // ),
          headerRight: () => (
            <>
              {/* <Coin /> */}
              <Entypo
                name="new-message"
                size={24}
                color="white"
                style={{ marginRight: 10 }}
                onPress={() => dispatch(initNewChat({ newChat: true }))}
              />
            </>
          ),
          headerTintColor: "white",
        })}
      />
      <Stack.Screen
        name="voiceAssistant"
        component={VoiceAssistantScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="gamification"
        component={GamificationScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export const ProfileStack = () => {
  return (
    <Stack.Navigator initialRouteName={"profile"}>
      <Stack.Screen
        name="profile"
        component={ProfileScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="subscription"
        component={SubscriptionScreen}
        options={{
          headerShown: false,
          presentation: "modal",
        }}
      />
    </Stack.Navigator>
  );
};

// Deep linking configuration
export const linking = {
  prefixes: ["fa.speakup.ai://"],
  config: {
    screens: {
      profileTab: {
        screens: {
          profile: "profile", // Define the profile screen path
          subscription: "subscription/:status", // Define the dynamic path for subscription screen
        },
      },
      topics: {
        screens: {
          topic: "topic", // Define the topic screen path
        },
      },
    },
  },
};

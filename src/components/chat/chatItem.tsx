import React, { memo, useEffect, useRef, useState } from "react";
import { View, Pressable, ActivityIndicator, Text } from "react-native";
import { MaterialIcons, MaterialCommunityIcons } from "@expo/vector-icons";
import {
  BotComponent,
  GrammerComponent,
  AvatarComponent,
} from "./chatItemComponents";

import GrammarPopup from "./GrammarPopup";
import RequestHelper from "../../utils/requestHelper";
import SuggestionBox from "../SuggestionBox";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import MessageComponent from "./MessageComponent";
import styles from "../../styles/chatItemStyle";
import { updateCoins } from "../../store/slices/authSlice";

interface ChatItemProps {
  item: {
    type: string;
    text: string;
    correctedText?: string;
    _id: string;
  };
  previousItem: any;
  nextItem: any;
  isSpeaking: any;
  setIsSpeaking: (index: number | null) => void;
  Speech: any;
  chatId: string;
  isLastMessage: boolean;
  index: number;
  playSpeech: boolean;
  flatListRef: any;
  lastIndex: number;
  textSubmitted: boolean;
  chatFinished: boolean;
  ScrollEnd: () => void;
  reward: number;
  setReward: (reward: number) => void;
  setShowSuccessMessage: (show: boolean) => void;
}

const ChatItemComponent = ({
  item,
  previousItem,
  nextItem,
  isSpeaking,
  setIsSpeaking,
  Speech,
  chatId,
  isLastMessage,
  index,
  playSpeech,
  flatListRef,
  lastIndex,
  textSubmitted,
  chatFinished,
  ScrollEnd,
  reward,
  setReward,
  setShowSuccessMessage,
}: ChatItemProps) => {
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [translatedText, setTranslatedText] = useState("");
  const [loadingTranslations, setLoadingTranslations] = useState(false);
  const [showOriginalText, setShowOriginalText] = useState(true);
  const { photo, coins } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const [isGrammarCorrect, setIsGrammarCorrect] = useState<boolean | null>(
    null
  );
  const [correctedText, setCorrectedText] = useState<string>("");
  const [grammarModalVisible, setGrammarModalVisible] = useState(false);

  useEffect(() => {
    if (item._id && item.type === "req") {
      if (item.correctedText) {
        setIsGrammarCorrect(false);
        setCorrectedText(item.correctedText);
      } else {
        setIsGrammarCorrect(true);
      }
    }
  }, [item]);

  const SpeechSpeak = () => {
    if (!chatFinished) {
      Speech.speak(item.text, {
        rate: 0.8,
        pitch: 1,
        language: "en-US",
        onDone: () => setIsSpeaking(null),
      });
    }
  };

  useEffect(() => {
    if (item.type === "res" && playSpeech && isLastMessage) {
      SpeechSpeak();
      setIsSpeaking(index);
    }
  }, [item, playSpeech]);

  useEffect(() => {
    if (
      (index === lastIndex ||
        index === lastIndex + 1 ||
        index === lastIndex + 2) &&
      item.type === "req" &&
      textSubmitted
    ) {
      checkGrammer();
    }
  }, [item.text]);

  async function getTranslation(text: string) {
    if (!translatedText) {
      setLoadingTranslations(true);
      const {
        message: { content },
      } = await RequestHelper("get", `/ai/prompt/translate/?q=${text}`);
      setLoadingTranslations(false);
      setShowOriginalText(false);
      setTranslatedText(content.trim());
    } else {
      setShowOriginalText(!showOriginalText);
    }
  }

  const checkGrammer = async () => {
    try {
      const { result } = await RequestHelper("post", `/ai/grammer`, {
        text: item.text,
      });
      if (result.correct) {
        setIsGrammarCorrect(true);
      } else {
        setIsGrammarCorrect(false);
        setCorrectedText(result.corrected);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const Icons = () => {
    return (
      <View
        style={{
          flex: 1,
          flexDirection: "row",
          justifyContent: "space-between",
        }}
      >
        <View
          style={{
            flexDirection: "row",
            gap: 22,
          }}
        >
          <MaterialCommunityIcons
            onPress={() => {
              if (isSpeaking > -1) {
                Speech.stop();
                if (isSpeaking === index) {
                  setIsSpeaking(null);
                } else {
                  setIsSpeaking(index);
                  SpeechSpeak();
                }
              } else {
                setIsSpeaking(index);
                SpeechSpeak();
              }
            }}
            name={isSpeaking === index ? "stop-circle-outline" : "play-circle-outline"}
            size={23}
            style={{ ...styles.icon, color: "#f0f0f0", bottom: 2 }}
          />
          {loadingTranslations ? (
            <ActivityIndicator size="small" color="#f0f0f0" />
          ) : (
            <MaterialIcons
              onPress={() => {
                getTranslation(item.text);
              }}
              name="translate"
              size={21}
              color="#f0f0f0"
            />
          )}
          {isLastMessage && (
            <Pressable
              hitSlop={10}
              onPress={() => {
                setShowSuggestion(!showSuggestion);
              }}
            >
              <MaterialCommunityIcons
                style={{ bottom: 2 }}
                name="lightbulb-on-outline"
                size={24}
                color="#f7eb00"
              />
            </Pressable>
          )}
        </View>
      </View>
    );
  };

  let justifyContent: "flex-end" | "flex-start";
  if (item.type === "req" || item.type === "loadingReq") {
    justifyContent = "flex-end";
  } else {
    justifyContent = "flex-start";
  }
  return (
    <>
      {showSuggestion && (
        <SuggestionBox
          showSuggestion={showSuggestion}
          isLastMessage={isLastMessage}
          item={item}
          chatId={chatId}
          flatListRef={flatListRef}
          lastIndex={lastIndex}
          ScrollEnd={ScrollEnd}
        />
      )}
      <View style={[styles.container, { justifyContent }]}>
        <BotComponent
          item={item}
          isLastMessage={isLastMessage}
          nextItem={nextItem}
          previousItem={previousItem}
          styles={styles}
        />
        <GrammerComponent
          item={item}
          isGrammarCorrect={isGrammarCorrect}
          reward={reward}
          setGrammarModalVisible={setGrammarModalVisible}
        />
        <MessageComponent
          item={item}
          previousItem={previousItem}
          nextItem={nextItem}
          isSpeaking={isSpeaking}
          isGrammarCorrect={isGrammarCorrect}
          showOriginalText={showOriginalText}
          translatedText={translatedText}
          Icons={Icons}
          setGrammarModalVisible={setGrammarModalVisible}
          correctedText={correctedText}
          ScrollEnd={ScrollEnd}
        />
        <AvatarComponent item={item} photo={photo} styles={styles} />
      </View>

      {grammarModalVisible && (
        <GrammarPopup
          visible={grammarModalVisible}
          originalText={item.text}
          correctedText={correctedText}
          onClose={() => setGrammarModalVisible(false)}
        />
      )}
    </>
  );
};

export default memo(ChatItemComponent);

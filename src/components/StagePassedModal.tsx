import { useNavigation } from "@react-navigation/native";
import React from "react";
import { View, Text, TouchableOpacity, StyleSheet, Modal } from "react-native";

//  Alert.alert(
//    "تبریک",
//    "شما با موفقیت این مرحله را انجام دادید",
//    [
//      {
//        text: "ادامه چت",
//        onPress: () => {},
//      },
//      {
//        text: "برو به صفحه موضوعات",
//        onPress: () => navigation.navigate("topic"),
//        style: "cancel",
//      },
//    ],
//    { cancelable: true }
//  );

const CustomAlert = ({ isVisible, setIsVisible }) => {
  const navigation = useNavigation();

  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="fade"
      onRequestClose={() => {
        setIsVisible(!isVisible);
      }}
    >
      <View style={styles.modalBackground}>
        <View style={styles.alertContainer}>
          <Text style={styles.alertTitle}>تبریک</Text>
          <Text style={styles.alertMessage}>
            موفق شدی این مرحله رو انجام بدی
          </Text>
          <TouchableOpacity
            onPress={() => setIsVisible(!isVisible)}
            style={styles.alertButton}
          >
            <Text style={styles.buttonText}>ادامه چت</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate("topic")}
            style={styles.alertButton}
          >
            <Text style={styles.buttonText}>برو به صفحه موضوعات</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  alertContainer: {
    width: 300,
    padding: 20,
    backgroundColor: "#fff",
    borderRadius: 10,
    alignItems: "center",
  },
  alertTitle: {
    fontSize: 20,
    // fontWeight: "bold",
    marginBottom: 10,
    color: "green",
    fontFamily: "EstedadBold",
  },
  alertMessage: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
    color: "#666",
    fontFamily: "EstedadRegular",
  },
  alertButton: {
    backgroundColor: "#6200EE",
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 7,
    width: 200
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontFamily: "EstedadRegular",
  },
});

export default CustomAlert;

import React, { useEffect, useState } from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import MP3Recording from "../components/MP3Recording";
import Recording from "../components/Recording";
// import { loadModel, runModel } from "../utils/whisperModel";
import { useAppSelector } from "../store/hooks";
import RequestHelper from "../utils/requestHelper";
import RNFS from "react-native-fs";

const TranscriptionTest = () => {
  const [localTranscription, setLocalTranscription] = useState("");
  const [groqTranscription, setgroqTranscription] = useState("");
  const [deepInfraTranscription, setDeepInfraTranscription] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [mp3Transcription, setMp3Transcription] = useState("");
  // useEffect(() => {
  //   loadModel();
  // }, []);
  const { textOfSpeech } = useAppSelector((state) => state.model);

  const handleRecordingCompleteMp3 = async (audioBuffer: any[]) => {
    setIsProcessing(true);
    try {
      // Process with local model
      // await runModel(audioBuffer);
      const filePathMp3 = RNFS.DocumentDirectoryPath + "/test.mp3";
      const filePath = RNFS.DocumentDirectoryPath + "/test.wav";
      // Create form data
      // const formData = new FormData();
      // formData.append("file", {
      //   uri: `file://${filePath}`,
      //   type: "audio/wav",
      //   name: "test.wav",
      // });

      // // Send to transcription endpoint
      // const data = await RequestHelper("post", "/transcribe/groq", formData, {
      //   "Content-Type": "multipart/form-data",
      // });
      // // Process with cloud API
      // setgroqTranscription(data.transcription || "");

      // // Send to DeepInfra endpoint
      // const deepInfraData = await RequestHelper("post", "/transcribe/deepinfra", formData, {
      //   "Content-Type": "multipart/form-data",
      // });
      // setDeepInfraTranscription(deepInfraData.transcription || "");
      // Inside handleRecordingComplete function, after deepInfraData
      // Send MP3 file for transcription
      const mp3FormData = new FormData();
      mp3FormData.append("file", {
        uri: `file://${filePath}`,
        type: "audio/wav",
        name: "test.wav",
      });
      const groqData = await RequestHelper(
        "post",
        "/transcribe/groq",
        mp3FormData,
        {
          "Content-Type": "multipart/form-data",
        }
      );
      setgroqTranscription(groqData.transcription || "");

      const deepInfraData = await RequestHelper(
        "post",
        "/transcribe/deepinfra",
        mp3FormData,
        {
          "Content-Type": "multipart/form-data",
        }
      );
      setDeepInfraTranscription(deepInfraData.transcription || "");
    } catch (error) {
      console.error("Transcription error:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRecordingCompleteWav = async (audioBuffer: any[]) => {
    setIsProcessing(true);
    try {
      const filePath = RNFS.DocumentDirectoryPath + "/test.wav";
      const mp3FormData = new FormData();
      mp3FormData.append("file", {
        uri: `file://${filePath}`,
        type: "audio/wav",
        name: "test.wav",
      });
      const groqData = await RequestHelper(
        "post",
        "/transcribe/groq",
        mp3FormData,
        {
          "Content-Type": "multipart/form-data",
        }
      );
      setgroqTranscription(groqData.transcription || "");

      const deepInfraData = await RequestHelper(
        "post",
        "/transcribe/deepinfra",
        mp3FormData,
        {
          "Content-Type": "multipart/form-data",
        }
      );
      setDeepInfraTranscription(deepInfraData.transcription || "");
    } catch (error) {
      console.error("Transcription error:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.transcriptionContainer}>
          <View style={styles.modelSection}>
            <Text style={styles.title}>Local Model</Text>
            <View style={styles.resultBox}>
              <Text style={styles.transcriptionText}>
                {isProcessing
                  ? "Processing..."
                  : textOfSpeech || "No transcription yet"}
              </Text>
            </View>
          </View>
          <View style={styles.modelSection}>
            <Text style={styles.title}> (Groq)</Text>
            <View style={styles.resultBox}>
              <Text style={styles.transcriptionText}>
                {isProcessing
                  ? "Processing..."
                  : groqTranscription || "No transcription yet"}
              </Text>
            </View>
          </View>
          <View style={styles.modelSection}>
            <Text style={styles.title}> (DeepInfra)</Text>
            <View style={styles.resultBox}>
              <Text style={styles.transcriptionText}>
                {isProcessing
                  ? "Processing..."
                  : deepInfraTranscription || "No transcription yet"}
              </Text>
            </View>
          </View>
          {/* <View style={styles.modelSection}>
            <Text style={styles.title}>MP3 Model (Groq)</Text>
            <View style={styles.resultBox}>
              <Text style={styles.transcriptionText}>
                {isProcessing
                  ? "Processing..."
                  : mp3Transcription || "No transcription yet"}
              </Text>
            </View>
          </View> */}
        </View>
      </ScrollView>

      <View style={styles.recordingContainer}>
        <MP3Recording onRecordingComplete={handleRecordingCompleteMp3} />
        <Recording onRecordingComplete={handleRecordingCompleteWav} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
  },
  transcriptionContainer: {
    padding: 16,
  },
  modelSection: {
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#333",
  },
  resultBox: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 16,
    minHeight: 100,
  },
  transcriptionText: {
    fontSize: 16,
    color: "#444",
    lineHeight: 24,
  },
  recordingContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    backgroundColor: "#fff",
  },
});

export default TranscriptionTest;
